/**
 * 统一矩阵状态管理
 * 🎯 核心价值：单一数据源，数据驱动视图，高性能计算属性
 * 📦 功能范围：矩阵数据、配置管理、计算属性、性能监控
 * 🔄 架构设计：基于Zustand的响应式状态管理，支持持久化和计算属性缓存
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { produce, enableMapSet } from 'immer';

// 启用 Immer 的 MapSet 插件以支持 Map 和 Set 数据结构
enableMapSet();
import type {
  MatrixData,
  MatrixConfig,
  CellData,
  ProcessedMatrixData,
  ComputedCache,
  PerformanceMetrics,
  BusinessMode,
  Coordinate,
  ColorType,
} from './MatrixTypes';

import {
  DEFAULT_MATRIX_CONFIG,
  coordinateKey,
  createDefaultCell,
  MATRIX_SIZE,
} from './MatrixTypes';

import {
  OPTIMIZED_GROUP_A_DATA,
  getMatrixDataByCoordinate,
  hasMatrixData,
  type MatrixDataSet,
} from '../data/GroupAData';

import { matrixCore } from './MatrixCore';

// ===== 状态接口 =====

interface MatrixStoreState {
  // 核心数据
  data: MatrixData;
  config: MatrixConfig;

  // 完整矩阵数据（A-M组）
  matrixData: MatrixDataSet;

  // 计算属性缓存
  cache: ComputedCache;

  // 性能监控
  metrics: PerformanceMetrics;

  // 状态标识
  isLoading: boolean;
  isDirty: boolean;
  lastUpdate: number;
}

interface MatrixStoreActions {
  // 数据操作
  initializeMatrix: () => void;
  updateCell: (x: number, y: number, updates: Partial<CellData>) => void;
  updateCells: (updates: Array<{ x: number; y: number; data: Partial<CellData> }>) => void;
  clearMatrix: () => void;
  
  // 配置操作
  setMode: (mode: BusinessMode) => void;
  updateConfig: (updates: Partial<MatrixConfig>) => void;
  resetConfig: () => void;
  
  // 交互操作
  selectCell: (x: number, y: number, multiSelect?: boolean) => void;
  selectCells: (coordinates: Coordinate[]) => void;
  clearSelection: () => void;
  hoverCell: (x: number, y: number) => void;
  focusCell: (x: number, y: number) => void;
  
  // 计算属性
  getProcessedData: () => ProcessedMatrixData;
  getCellRenderData: (x: number, y: number) => any;
  
  // 矩阵数据操作
  getMatrixDataByCoordinate: (x: number, y: number) => any;
  hasMatrixData: (x: number, y: number) => boolean;

  // 缓存管理
  invalidateCache: () => void;
  updateCache: () => void;

  // 性能监控
  startPerformanceTracking: () => void;
  endPerformanceTracking: (operation: string) => void;
}

type MatrixStore = MatrixStoreState & MatrixStoreActions;

// ===== 初始状态 =====

const createInitialData = (): MatrixData => ({
  cells: new Map(),
  selectedCells: new Set(),
  hoveredCell: null,
  focusedCell: null,
});

const createInitialCache = (): ComputedCache => ({
  cellStyles: new Map(),
  cellContents: new Map(),
  cellClassNames: new Map(),
  interactionStates: new Map(),
  lastUpdate: 0,
});

const createInitialMetrics = (): PerformanceMetrics => ({
  renderTime: 0,
  updateTime: 0,
  cacheHitRate: 0,
  memoryUsage: 0,
  frameRate: 60,
});

// ===== 计算属性函数 =====

const computeCellContent = (cell: CellData, config: MatrixConfig): string => {
  switch (config.mode) {
    case 'coordinate':
      return `${cell.x},${cell.y}`;
    case 'color':
      return cell.color || '';
    case 'value':
      return cell.value?.toString() || '';
    case 'word':
      return cell.word || '';
    default:
      return '';
  }
};

const computeCellStyle = (cell: CellData, config: MatrixConfig): any => {
  const baseStyle: any = {
    width: `${config.cellSize}px`,
    height: `${config.cellSize}px`,
  };

  if (cell.isSelected) {
    baseStyle.border = '2px solid #3b82f6';
  }

  if (cell.isHovered) {
    baseStyle.backgroundColor = '#f3f4f6';
  }

  if (cell.color && config.showColors) {
    const colorMap = {
      red: '#ef4444',
      cyan: '#06b6d4',
      yellow: '#eab308',
      purple: '#a855f7',
      orange: '#f97316',
      green: '#22c55e',
      blue: '#3b82f6',
      pink: '#ec4899',
    };
    baseStyle.backgroundColor = colorMap[cell.color];
  }

  return baseStyle;
};

const computeCellClassName = (cell: CellData, _config: MatrixConfig): string => {
  const classes = ['matrix-cell'];
  
  if (cell.isActive) classes.push('active');
  if (cell.isSelected) classes.push('selected');
  if (cell.isHovered) classes.push('hovered');
  if (cell.color) classes.push(`color-${cell.color}`);
  if (cell.level) classes.push(`level-${cell.level}`);
  
  return classes.join(' ');
};

// ===== Store实现 =====

export const useMatrixStore = create<MatrixStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      data: createInitialData(),
      config: DEFAULT_MATRIX_CONFIG,
      matrixData: OPTIMIZED_GROUP_A_DATA,
      cache: createInitialCache(),
      metrics: createInitialMetrics(),
      isLoading: false,
      isDirty: false,
      lastUpdate: Date.now(),
      
      // 数据操作
      initializeMatrix: () => {
        const startTime = performance.now();
        console.log('=== initializeMatrix 开始执行 ===');

        set(produce((state) => {
          state.data.cells.clear();

          // 初始化所有单元格
          for (let x = 0; x < MATRIX_SIZE; x++) {
            for (let y = 0; y < MATRIX_SIZE; y++) {
              const key = coordinateKey(x, y);
              const cell = createDefaultCell(x, y);

              // 从A组数据中获取颜色信息
              console.log(`检查坐标 (${x},${y}), matrixData长度:`, state.matrixData.length);
              const matrixDataPoint = getMatrixDataByCoordinate(state.matrixData, x, y);
              console.log(`检查坐标 (${x},${y}), 数据点:`, matrixDataPoint);
              if (matrixDataPoint) {
                // 过滤掉黑色，因为CellData.color不包含黑色
                if (matrixDataPoint.color !== 'black') {
                  cell.color = matrixDataPoint.color as ColorType;
                  console.log(`设置单元格 (${x},${y}) 颜色为: ${matrixDataPoint.color}`);
                }
                cell.level = matrixDataPoint.level;
                cell.value = matrixDataPoint.level; // 设置数值为层级
              } else {
                console.log(`坐标 (${x},${y}) 没有找到数据点`);
              }

              state.data.cells.set(key, cell);
            }
          }

          state.isDirty = true;
          state.lastUpdate = Date.now();
        }));

        const endTime = performance.now();
        get().endPerformanceTracking(`initializeMatrix: ${endTime - startTime}ms`);
      },
      
      updateCell: (x: number, y: number, updates: Partial<CellData>) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);
          const cell = state.data.cells.get(key);
          
          if (cell) {
            Object.assign(cell, updates);
            state.isDirty = true;
            state.lastUpdate = Date.now();
          }
        }));
        
        get().invalidateCache();
      },
      
      updateCells: (updates) => {
        set(produce((state) => {
          updates.forEach(({ x, y, data }) => {
            const key = coordinateKey(x, y);
            const cell = state.data.cells.get(key);
            
            if (cell) {
              Object.assign(cell, data);
            }
          });
          
          state.isDirty = true;
          state.lastUpdate = Date.now();
        }));
        
        get().invalidateCache();
      },
      
      clearMatrix: () => {
        set(produce((state) => {
          state.data.cells.clear();
          state.data.selectedCells.clear();
          state.data.hoveredCell = null;
          state.data.focusedCell = null;
          state.isDirty = true;
          state.lastUpdate = Date.now();
        }));
        
        get().invalidateCache();
      },
      
      // 配置操作
      setMode: (mode: BusinessMode) => {
        set(produce((state) => {
          state.config.mode = mode;
          state.isDirty = true;
          state.lastUpdate = Date.now();
        }));
        
        get().invalidateCache();
      },
      
      updateConfig: (updates: Partial<MatrixConfig>) => {
        set(produce((state) => {
          Object.assign(state.config, updates);
          state.isDirty = true;
          state.lastUpdate = Date.now();
        }));
        
        get().invalidateCache();
      },
      
      resetConfig: () => {
        set(produce((state) => {
          state.config = { ...DEFAULT_MATRIX_CONFIG };
          state.isDirty = true;
          state.lastUpdate = Date.now();
        }));
        
        get().invalidateCache();
      },
      
      // 交互操作
      selectCell: (x: number, y: number, multiSelect = false) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);
          
          if (!multiSelect) {
            state.data.selectedCells.clear();
            // 清除所有单元格的选中状态
            state.data.cells.forEach((cell: CellData) => {
              cell.isSelected = false;
            });
          }
          
          state.data.selectedCells.add(key);
          const cell = state.data.cells.get(key);
          if (cell) {
            cell.isSelected = true;
          }
          
          state.lastUpdate = Date.now();
        }));
      },
      
      selectCells: (coordinates: Coordinate[]) => {
        set(produce((state) => {
          state.data.selectedCells.clear();
          
          // 清除所有选中状态
          state.data.cells.forEach((cell: CellData) => {
            cell.isSelected = false;
          });
          
          // 设置新的选中状态
          coordinates.forEach(({ x, y }) => {
            const key = coordinateKey(x, y);
            state.data.selectedCells.add(key);
            const cell = state.data.cells.get(key);
            if (cell) {
              cell.isSelected = true;
            }
          });
          
          state.lastUpdate = Date.now();
        }));
      },
      
      clearSelection: () => {
        set(produce((state) => {
          state.data.selectedCells.clear();
          state.data.cells.forEach((cell: CellData) => {
            cell.isSelected = false;
          });
          state.lastUpdate = Date.now();
        }));
      },
      
      hoverCell: (x: number, y: number) => {
        set(produce((state) => {
          // 清除之前的悬停状态
          if (state.data.hoveredCell) {
            const prevCell = state.data.cells.get(state.data.hoveredCell);
            if (prevCell) {
              prevCell.isHovered = false;
            }
          }
          
          // 设置新的悬停状态
          const key = coordinateKey(x, y);
          state.data.hoveredCell = key;
          const cell = state.data.cells.get(key);
          if (cell) {
            cell.isHovered = true;
          }
          
          state.lastUpdate = Date.now();
        }));
      },
      
      focusCell: (x: number, y: number) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);
          state.data.focusedCell = key;
          state.lastUpdate = Date.now();
        }));
      },
      
      // 计算属性
      getProcessedData: (): ProcessedMatrixData => {
        const state = get();
        // 使用矩阵核心引擎处理数据
        return matrixCore.processData(state.data, state.config);
      },
      
      getCellRenderData: (x: number, y: number) => {
        const state = get();
        const key = coordinateKey(x, y);
        const cell = state.data.cells.get(key);
        
        if (!cell) return null;
        
        return {
          content: computeCellContent(cell, state.config),
          style: computeCellStyle(cell, state.config),
          className: computeCellClassName(cell, state.config),
          isInteractive: cell.isActive,
        };
      },
      
      // 缓存管理
      invalidateCache: () => {
        set(produce((state) => {
          state.cache.cellStyles.clear();
          state.cache.cellContents.clear();
          state.cache.cellClassNames.clear();
          state.cache.interactionStates.clear();
          state.cache.lastUpdate = Date.now();
        }));
      },
      
      updateCache: () => {
        const state = get();
        const processedData = state.getProcessedData();
        
        set(produce((draft) => {
          draft.cache.cellStyles.clear();
          draft.cache.cellContents.clear();
          draft.cache.cellClassNames.clear();
          
          processedData.renderData.forEach((renderData, key) => {
            draft.cache.cellStyles.set(key, renderData.style);
            draft.cache.cellContents.set(key, renderData.content);
            draft.cache.cellClassNames.set(key, renderData.className);
          });
          
          draft.cache.lastUpdate = Date.now();
        }));
      },
      
      // 矩阵数据操作
      getMatrixDataByCoordinate: (x: number, y: number) => {
        return getMatrixDataByCoordinate(get().matrixData, x, y);
      },

      hasMatrixData: (x: number, y: number) => {
        return hasMatrixData(get().matrixData, x, y);
      },

      // 性能监控
      startPerformanceTracking: () => {
        // 性能监控开始
      },

      endPerformanceTracking: (operation: string) => {
        console.log(`Performance: ${operation}`);
      },
    }),
    {
      name: 'matrix-store',
      version: 1,
      // 只持久化配置和基础数据，不持久化缓存
      partialize: (state) => ({
        config: state.config,
        data: {
          ...state.data,
          cells: Array.from(state.data.cells.entries()),
          selectedCells: Array.from(state.data.selectedCells),
        },
      }),
      // 反序列化时重建Map和Set
      onRehydrateStorage: () => (state) => {
        if (state?.data) {
          state.data.cells = new Map(state.data.cells as any);
          state.data.selectedCells = new Set(state.data.selectedCells as any);
        }
      },
    }
  )
);

// ===== 选择器钩子 =====

export const useMatrixData = () => useMatrixStore((state) => state.data);
export const useMatrixConfig = () => useMatrixStore((state) => state.config);
export const useMatrixMode = () => useMatrixStore((state) => state.config.mode);
export const useSelectedCells = () => useMatrixStore((state) => state.data.selectedCells);
export const useHoveredCell = () => useMatrixStore((state) => state.data.hoveredCell);
export const useFocusedCell = () => useMatrixStore((state) => state.data.focusedCell);
export const useMatrixMetrics = () => useMatrixStore((state) => state.metrics);
export const useIsMatrixDirty = () => useMatrixStore((state) => state.isDirty);
